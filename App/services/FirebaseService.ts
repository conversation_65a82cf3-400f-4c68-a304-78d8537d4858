
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Alert } from 'react-native';
import axiosInstance from '../config/axios';

interface DeviceInfo {
  platform: string;
  version: string | number;
}

interface TokenRegistrationData {
  fcmToken: string;
  platform: string;
  deviceInfo: DeviceInfo;
}

interface NotificationData {
  actionType?: string;
  screen?: string;
  params?: any;
  [key: string]: any;
}

class FirebaseService {
  private isInitialized: boolean = false;
  private fcmToken: string | null = null;

  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
  }

  async initialize(): Promise<void> {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        await this.getFCMToken();
        this.setupMessageHandlers();

        // Setup token refresh listener
        this.setupTokenRefreshListener();

        this.isInitialized = true;
      }
    } catch (error) {
    }
  }

  async getFCMToken(): Promise<string | null> {
    try {
      if (!messaging().isDeviceRegisteredForRemoteMessages) {
        await messaging().registerDeviceForRemoteMessages();
      }

      const token = await messaging().getToken();
      if (token) {
        this.fcmToken = token;
        await AsyncStorage.setItem('fcm_token', token);
        await this.sendTokenToBackend(token);
        return token;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  private async sendTokenToBackend(token: string): Promise<void> {
    try {
      const authToken = await AsyncStorage.getItem('token');
      if (!authToken) {
        return;
      }

      const tokenData = {
        token: token,
        platform: Platform.OS,
        deviceId: Platform.OS === 'android' ? 'android-device' : 'ios-device',
      };

      await axiosInstance.post('/fcm-tokens/student/register', tokenData);
    } catch (error: any) {
    }
  }



  private setupMessageHandlers(): void {
    messaging().onMessage(async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      this.handleForegroundMessage(remoteMessage);
    });

    messaging().onNotificationOpenedApp((remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      this.handleNotificationPress(remoteMessage);
    });

    messaging()
      .getInitialNotification()
      .then((remoteMessage: FirebaseMessagingTypes.RemoteMessage | null) => {
        if (remoteMessage) {
          this.handleNotificationPress(remoteMessage);
        }
      });
  }

  private setupTokenRefreshListener(): void {
    messaging().onTokenRefresh((token: string) => {
      this.fcmToken = token;
      AsyncStorage.setItem('fcm_token', token);
      this.sendTokenToBackend(token);
    });
  }



  private async handleForegroundMessage(remoteMessage: FirebaseMessagingTypes.RemoteMessage): Promise<void> {
    if ((global as any).refreshNotificationCount) {
      (global as any).refreshNotificationCount();
    }

    if (remoteMessage.notification) {
      Alert.alert(
        remoteMessage.notification.title || 'New Notification',
        remoteMessage.notification.body || 'You have a new message',
        [
          {
            text: 'View',
            onPress: () => {
              if (remoteMessage.data) {
                this.handleNotificationPress(remoteMessage);
              }
            },
          },
          {
            text: 'Dismiss',
            style: 'cancel',
          },
        ]
      );
    }
  }

  private handleNotificationPress(remoteMessage: FirebaseMessagingTypes.RemoteMessage): void {
    if (remoteMessage.data) {
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      const token = await AsyncStorage.getItem('fcm_token');
      return token;
    } catch (error) {
      return null;
    }
  }

  isFirebaseInitialized(): boolean {
    return this.isInitialized;
  }

  getCurrentToken(): string | null {
    return this.fcmToken;
  }

  async refreshTokenRegistration(): Promise<void> {
    if (this.fcmToken) {
      await this.sendTokenToBackend(this.fcmToken);
    }
  }

  async registerTokenAfterAuth(): Promise<void> {
    try {
      const token = await this.getFCMToken();
      if (token) {
        await this.sendTokenToBackend(token);
      }
    } catch (error) {
    }
  }
}

const firebaseService = new FirebaseService();

export default firebaseService;
export type { NotificationData, TokenRegistrationData, DeviceInfo };
