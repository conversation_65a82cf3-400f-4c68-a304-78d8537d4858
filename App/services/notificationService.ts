import axiosInstance from '../config/axios';

export interface Notification {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS' | 'ADMIN';
  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |
        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |
        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |
        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |
        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |
        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: NotificationPagination;
}

export const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  try {
    const response = await axiosInstance.get('/notifications/students', {
      params: { page, limit }
    });

    let notificationData: NotificationResponse;

    if (response.data.success && response.data.data) {
      notificationData = response.data.data;
    } else if (response.data.notifications) {
      notificationData = response.data;
    } else {
      notificationData = {
        notifications: [],
        pagination: {
          currentPage: page,
          totalPages: 1,
          totalCount: 0,
          limit,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }

    return notificationData;
  } catch (error) {
    throw error;
  }
};





export const getStudentUnreadCount = async (): Promise<number> => {
  try {
    const response = await axiosInstance.get('/notifications/students/count');

    if (response.data.success && response.data.data && typeof response.data.data.count === 'number') {
      return response.data.data.count;
    } else if (typeof response.data.count === 'number') {
      return response.data.count;
    } else {
      return 0;
    }
  } catch (error) {
    return 0;
  }
};

export const markStudentNotificationAsRead = async (notificationId: string): Promise<void> => {
  try {
    await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);
  } catch (error) {
    throw error;
  }
};

export const markAllStudentNotificationsAsRead = async (): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/students/mark-all-read');
  } catch (error) {
    throw error;
  }
};

export const deleteAllStudentNotifications = async (): Promise<void> => {
  try {
    await axiosInstance.delete('/notifications/students/delete-all');
  } catch (error) {
    throw error;
  }
};

export const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return 'person-add';
    case 'STUDENT_PROFILE_APPROVED':
      return 'shield-checkmark';
    case 'STUDENT_PROFILE_REJECTED':
      return 'close-circle';
    case 'STUDENT_COIN_PURCHASE':
      return 'card';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return 'trophy';
    case 'STUDENT_CHAT_MESSAGE':
      return 'chatbubble';
    default:
      return 'notifications';
  }
};

export const getNotificationColor = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return '#00FF00';
    case 'STUDENT_PROFILE_APPROVED':
      return '#00FF00';
    case 'STUDENT_PROFILE_REJECTED':
      return '#FF0000';
    case 'STUDENT_COIN_PURCHASE':
      return '#F66500';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return '#0000FF';
    case 'STUDENT_CHAT_MESSAGE':
      return '#FF914D';
    default:
      return '#737272';
  }
};



const refreshNotificationCount = () => {
  try {
    if ((global as any).refreshNotificationCount) {
      (global as any).refreshNotificationCount();
    }
  } catch (error) {
  }
};

export const triggerCoinPurchaseNotification = async (amount: number, coins: number): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_COIN_PURCHASE',
      title: 'Coins Purchased Successfully!',
      message: `You have successfully purchased ${coins} coins for ₹${amount}. Your coins have been added to your account.`,
      data: {
        actionType: 'OPEN_WALLET',
        amount,
        coins,
        timestamp: new Date().toISOString(),
      },
    });
    refreshNotificationCount();
  } catch (error) {
  }
};

export const triggerExamParticipationNotification = async (examName: string, result?: string): Promise<void> => {
  try {
    const title = result ? 'Exam Results Available!' : 'Exam Participation Confirmed';
    const message = result
      ? `Your results for ${examName} are now available. ${result}`
      : `You have successfully registered for ${examName}. Good luck!`;

    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_UWHIZ_PARTICIPATION',
      title,
      message,
      data: {
        actionType: 'OPEN_EXAM_RESULTS',
        examName,
        result,
        timestamp: new Date().toISOString(),
      },
    });
    refreshNotificationCount();
  } catch (error) {
  }
};
