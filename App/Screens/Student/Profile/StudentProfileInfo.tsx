import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { RootState } from '../../../Redux/store';
import { PrimaryColors } from '../../../Utils/Constants';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import { getStudentProfileData, updateStudentProfileData, getConstantsByCategory } from '../../../services/studentProfileService';
import { getCurrentStudent } from '../../../services/studentService';
import { imgBaseUrl } from '../../../config/apiUrl';
import Toast from 'react-native-simple-toast';
import CommonDateTimePicker from '../../../CommonComponents/CommonDateTimePicker';
import CommonDropdownPicker from '../../../CommonComponents/CommonDropdownPicker';
import CommonFileUpload from '../../../CommonComponents/CommonFileUpload';
import { launchImageLibrary, launchCamera } from 'react-native-image-picker';
import { PermissionsAndroid, Platform } from 'react-native';
import { DocumentPickerResponse } from '@react-native-documents/picker';

interface StudentData {
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  middleName?: string;
  mothersName?: string;
  profilePhoto?: string;
}

interface ProfileData {
  medium?: string;
  classroom?: string;
  birthday?: string;
  school?: string;
  address?: string;
  gender?: string;
  age?: number;
  aadhaarNo?: string;
  bloodGroup?: string;
  birthPlace?: string;
  motherTongue?: string;
  religion?: string;
  caste?: string;
  subCaste?: string;
  photo?: string;
  contactNo2?: string;
  documentUrl?: string;
}

interface EditableFieldProps {
  label: string;
  value: string | number | undefined;
  onChangeText: (text: string) => void;
  isEditMode: boolean;
  isDarkMode: boolean;
  placeholder?: string;
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad';
  multiline?: boolean;
  error?: string;
}

const EditableField: React.FC<EditableFieldProps> = ({
  label,
  value,
  onChangeText,
  isEditMode,
  isDarkMode,
  placeholder,
  keyboardType = 'default',
  multiline = false,
  error,
}) => (
  <View style={styles.infoItem}>
    <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
      {label}
    </Text>
    {isEditMode ? (
      <>
        <TextInput
          style={[
            styles.infoInput,
            {
              color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              backgroundColor: isDarkMode ? '#2A2A2A' : '#F8F8F8',
              borderColor: error ? '#F44336' : (isDarkMode ? '#404040' : '#E8E8E8'),
            },
            multiline && { minHeight: 80, textAlignVertical: 'top' }
          ]}
          value={value?.toString() || ''}
          onChangeText={onChangeText}
          placeholder={placeholder || `Enter ${label.toLowerCase()}`}
          placeholderTextColor={isDarkMode ? '#666666' : '#AAAAAA'}
          keyboardType={keyboardType}
          multiline={multiline}
        />
        {error && (
          <Text style={styles.errorText}>
            {error}
          </Text>
        )}
      </>
    ) : (
      <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
        {value || 'Not provided'}
      </Text>
    )}
  </View>
);

interface EditableDropdownFieldProps {
  label: string;
  value: string | undefined;
  onChange: (value: string) => void;
  isEditMode: boolean;
  isDarkMode: boolean;
  options: { label: string; value: string }[];
  placeholder?: string;
  error?: string;
}

const EditableDropdownField: React.FC<EditableDropdownFieldProps> = ({
  label,
  value,
  onChange,
  isEditMode,
  isDarkMode,
  options,
  placeholder,
  error,
}) => (
  <View style={styles.infoItem}>
    <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
      {label}
    </Text>
    {isEditMode ? (
      <>
        <CommonDropdownPicker
          data={options}
          value={value}
          onChange={onChange}
          placeholder={placeholder || `Select ${label.toLowerCase()}`}
          label=""
          style={{ marginBottom: 0 }}
        />
        {error && (
          <Text style={styles.errorText}>
            {error}
          </Text>
        )}
      </>
    ) : (
      <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
        {value || 'Not provided'}
      </Text>
    )}
  </View>
);

interface EditableDateFieldProps {
  label: string;
  value: Date | string | undefined;
  onChange: (date: Date) => void;
  isEditMode: boolean;
  isDarkMode: boolean;
  error?: string;
}

const EditableDateField: React.FC<EditableDateFieldProps> = ({
  label,
  value,
  onChange,
  isEditMode,
  isDarkMode,
  error,
}) => {
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'Not provided';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString('en-GB');
    } catch {
      return 'Invalid date';
    }
  };

  const getDateValue = () => {
    if (!value) return null;
    return typeof value === 'string' ? new Date(value) : value;
  };

  return (
    <View style={styles.infoItem}>
      <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
        {label}
      </Text>
      {isEditMode ? (
        <>
          <CommonDateTimePicker
            value={getDateValue()}
            innerText={`Select ${label.toLowerCase()}`}
            onChange={onChange}
            mode="date"
            style={{ width: '100%', marginBottom: 0 }}
          />
          {error && (
            <Text style={styles.errorText}>
              {error}
            </Text>
          )}
        </>
      ) : (
        <Text style={[styles.infoValue, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
          {formatDate(value)}
        </Text>
      )}
    </View>
  );
};

const StudentProfileInfo = () => {
  const navigation = useNavigation<any>();
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
  
  const [studentData, setStudentData] = useState<StudentData>({});
  const [profileData, setProfileData] = useState<ProfileData>({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('Personal');
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedStudentData, setEditedStudentData] = useState<StudentData>({});
  const [editedProfileData, setEditedProfileData] = useState<ProfileData>({});
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Dropdown options state
  const [mediumOptions, setMediumOptions] = useState<{label: string, value: string}[]>([]);
  const [classroomOptions, setClassroomOptions] = useState<{label: string, value: string}[]>([]);
  const [bloodGroupOptions] = useState<{label: string, value: string}[]>([
    { label: 'A+', value: 'A+' },
    { label: 'A-', value: 'A-' },
    { label: 'B+', value: 'B+' },
    { label: 'B-', value: 'B-' },
    { label: 'AB+', value: 'AB+' },
    { label: 'AB-', value: 'AB-' },
    { label: 'O+', value: 'O+' },
    { label: 'O-', value: 'O-' },
  ]);

  const [genderOptions] = useState<{label: string, value: string}[]>([
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'Female' },
    { label: 'Other', value: 'Other' },
  ]);

  // Image and document state
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<DocumentPickerResponse | null>(null);
  const [imageBase64, setImageBase64] = useState<string | null>(null);
  const [imageMimeType, setImageMimeType] = useState<string>('image/jpeg');

  useEffect(() => {
    fetchStudentData();
  }, []);

  // Reset edited data when toggling edit mode
  useEffect(() => {
    if (isEditMode) {
      setEditedStudentData({...studentData});
      setEditedProfileData({...profileData});
    }
  }, [isEditMode, studentData, profileData]);

  const fetchStudentData = async () => {
    try {
      setLoading(true);

      // Fetch basic student info
      const studentRes = await getCurrentStudent();
      console.log('Student Response:', studentRes);
      setStudentData(studentRes.data || {});

      // Fetch profile info (this includes classroomOptions)
      const profileRes = await getStudentProfileData();
      console.log('Profile Response:', profileRes);

      // Handle the profile data structure properly
      const profile = profileRes.data?.profile || {};
      const classroomOptionsFromApi = profileRes.data?.classroomOptions || [];
      console.log('Profile Data:', profile);
      console.log('Classroom Options:', classroomOptionsFromApi);

      // Set classroom options from API
      const formattedClassroomOptions = classroomOptionsFromApi.map((option: any) => ({
        label: option.value,
        value: option.value
      }));
      setClassroomOptions(formattedClassroomOptions);

      // Fetch medium options from constants API
      try {
        const constantsRes = await getConstantsByCategory('TuitionClasses');
        console.log('Constants Response:', constantsRes);

        // Extract medium options from constants
        const educationDetail = constantsRes?.details?.find((d: any) => d.name === 'Education');
        const mediumSubDetail = educationDetail?.subDetails?.find((sd: any) => sd.name === 'medium');
        const mediumOptionsFromApi = mediumSubDetail?.values?.map((val: any) => ({
          label: val.name,
          value: val.name
        })) || [];

        if (mediumOptionsFromApi.length > 0) {
          setMediumOptions(mediumOptionsFromApi);
        } else {
          // Fallback to hardcoded options
          setMediumOptions([
            { label: 'English', value: 'english' },
            { label: 'Gujarati', value: 'gujarati' },
            { label: 'Hindi', value: 'hindi' },
          ]);
        }
      } catch (constantsError) {
        console.error('Error fetching constants:', constantsError);
        // Fallback to hardcoded options
        setMediumOptions([
          { label: 'English', value: 'english' },
          { label: 'Gujarati', value: 'gujarati' },
          { label: 'Hindi', value: 'hindi' },
        ]);
      }

      // Also get student data from profile response if available
      if (profile.student) {
        console.log('Student data from profile:', profile.student);
        setStudentData(prev => ({
          ...prev,
          ...profile.student,
          middleName: profile.student.middleName || prev.middleName,
          mothersName: profile.student.mothersName || prev.mothersName,
        }));
      }

      // Set profile data with all the fields
      setProfileData({
        medium: profile.medium,
        classroom: profile.classroom,
        birthday: profile.birthday,
        school: profile.school,
        address: profile.address,
        gender: profile.gender,
        age: profile.age,
        aadhaarNo: profile.aadhaarNo,
        bloodGroup: profile.bloodGroup,
        birthPlace: profile.birthPlace,
        motherTongue: profile.motherTongue,
        religion: profile.religion,
        caste: profile.caste,
        subCaste: profile.subCaste,
        photo: profile.photo,
        contactNo2: profile.contactNo2,
        documentUrl: profile.documentUrl,
        ...profile
      });

      console.log('Final student data:', studentData);
      console.log('Final profile data:', profileData);

    } catch (error) {
      console.error('Error fetching student data:', error);
    } finally {
      setLoading(false);
    }
  };



  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    // Validate required fields (only if they have values)
    if (editedStudentData.firstName && editedStudentData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    }
    if (editedStudentData.lastName && editedStudentData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    }
    if (editedStudentData.email && !/\S+@\S+\.\S+/.test(editedStudentData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    if (editedStudentData.contact && !/^\d{10,15}$/.test(editedStudentData.contact.replace(/\D/g, ''))) {
      newErrors.contact = 'Contact number must be 10-15 digits';
    }
    if (editedProfileData.aadhaarNo && editedProfileData.aadhaarNo.trim() !== '') {
      const cleanAadhaar = editedProfileData.aadhaarNo.replace(/\D/g, '');
      if (cleanAadhaar.length !== 12) {
        newErrors.aadhaarNo = 'Aadhaar number must be exactly 12 digits';
      }
    }
    if (editedProfileData.school && editedProfileData.school.trim().length < 2) {
      newErrors.school = 'School name must be at least 2 characters';
    }
    if (editedProfileData.address && editedProfileData.address.trim().length < 5) {
      newErrors.address = 'Address must be at least 5 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Toast.show('Please fix the errors before saving', Toast.SHORT);
      return;
    }

    try {
      setSaving(true);

      // Prepare payload according to API schema
      const payload: any = {};

      // Student data fields
      if (editedStudentData.firstName) {
        payload.firstName = editedStudentData.firstName;
      }
      if (editedStudentData.lastName) {
        payload.lastName = editedStudentData.lastName;
      }
      if (editedStudentData.middleName) {
        payload.middleName = editedStudentData.middleName;
      }
      if (editedStudentData.mothersName) {
        payload.mothersName = editedStudentData.mothersName;
      }
      if (editedStudentData.email) {
        payload.email = editedStudentData.email;
      }
      if (editedStudentData.contact) {
        payload.contact = editedStudentData.contact;
      }

      // Profile data fields
      if (editedProfileData.contactNo2) {
        payload.contact2 = editedProfileData.contactNo2;
      }
      if (editedProfileData.gender) {
        payload.gender = editedProfileData.gender;
      }
      if (editedProfileData.medium) {
        payload.medium = editedProfileData.medium;
      }
      if (editedProfileData.classroom) {
        payload.classroom = editedProfileData.classroom;
      }
      if (editedProfileData.school) {
        payload.school = editedProfileData.school;
      }
      if (editedProfileData.address) {
        payload.address = editedProfileData.address;
      }
      if (editedProfileData.bloodGroup) {
        payload.bloodGroup = editedProfileData.bloodGroup;
      }
      if (editedProfileData.birthPlace) {
        payload.birthPlace = editedProfileData.birthPlace;
      }
      if (editedProfileData.motherTongue) {
        payload.motherTongue = editedProfileData.motherTongue;
      }
      if (editedProfileData.religion) {
        payload.religion = editedProfileData.religion;
      }
      if (editedProfileData.caste) {
        payload.caste = editedProfileData.caste;
      }
      if (editedProfileData.subCaste) {
        payload.subCaste = editedProfileData.subCaste;
      }

      // Handle special fields
      if (editedProfileData.birthday) {
        // Ensure birthday is in proper ISO string format
        const birthdayDate = typeof editedProfileData.birthday === 'string'
          ? new Date(editedProfileData.birthday)
          : editedProfileData.birthday;
        payload.birthday = birthdayDate.toISOString();
      }

      if (editedProfileData.age) {
        payload.age = editedProfileData.age.toString();
      }

      if (editedProfileData.aadhaarNo) {
        payload.aadhaarNumber = editedProfileData.aadhaarNo; // Note: API expects aadhaarNumber
      }

      // Handle image upload
      if (imageBase64) {
        payload.photo = imageBase64;
        payload.photoMimeType = imageMimeType;
      }

      // Handle document upload
      if (selectedDocument) {
        // For document upload, we'll need to handle file upload separately
        // This is a placeholder - you may need to implement file upload API
        payload.documentName = selectedDocument.name;
        payload.documentType = selectedDocument.type;
        payload.documentUri = selectedDocument.uri;
      }

      console.log('Sending payload:', payload);

      await updateStudentProfileData(payload);

      // Update the original data with saved changes
      setStudentData({...editedStudentData});
      setProfileData({...editedProfileData});

      setIsEditMode(false);
      setErrors({});
      Toast.show('Profile updated successfully!', Toast.SHORT);

    } catch (error: any) {
      console.error('Error updating profile:', error);
      console.error('Error response:', error.response?.data);

      // Show specific error message if available
      const errorMessage = error.response?.data?.message || 'Failed to update profile. Please try again.';
      Toast.show(errorMessage, Toast.SHORT);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedStudentData({...studentData});
    setEditedProfileData({...profileData});
    setSelectedImage(null);
    setSelectedDocument(null);
    setImageBase64(null);
    setIsEditMode(false);
  };

  // Image picker functions
  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Camera permission error:', err);
        return false;
      }
    }
    return true;
  };

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
          {
            title: 'Storage Permission',
            message: 'App needs access to your photo gallery.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (error) {
        console.log('Permission error:', error);
        return false;
      }
    }
    return true;
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Select Student Image',
      'Choose an option to select your profile image',
      [
        { text: 'Camera', onPress: openCamera },
        { text: 'Gallery', onPress: openGallery },
        { text: 'Remove Image', onPress: removeImage, style: 'destructive' },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Toast.show('Camera permission denied', Toast.SHORT);
      return;
    }

    const options = {
      mediaType: 'photo' as const,
      includeBase64: true,
      maxWidth: 800,
      maxHeight: 800,
      quality: 0.8 as const,
    };

    launchCamera(options, (response) => {
      if (response.didCancel || response.errorCode) {
        return;
      }
      const asset = response.assets?.[0];
      if (asset && asset.uri && asset.base64) {
        setSelectedImage(asset.uri);
        setImageBase64(asset.base64);
        setImageMimeType(asset.type || 'image/jpeg');
      }
    });
  };

  const openGallery = async () => {
    const hasPermission = await requestStoragePermission();
    if (!hasPermission) {
      Toast.show('Storage permission denied', Toast.SHORT);
      return;
    }

    const options = {
      mediaType: 'photo' as const,
      includeBase64: true,
      maxWidth: 800,
      maxHeight: 800,
      quality: 0.8 as const,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel || response.errorCode) {
        return;
      }
      const asset = response.assets?.[0];
      if (asset && asset.uri && asset.base64) {
        setSelectedImage(asset.uri);
        setImageBase64(asset.base64);
        setImageMimeType(asset.type || 'image/jpeg');
      }
    });
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImageBase64(null);
    setImageMimeType('image/jpeg');
  };

  const handleDocumentSelect = (file: DocumentPickerResponse) => {
    setSelectedDocument(file);
  };

  const removeDocument = () => {
    setSelectedDocument(null);
  };

  const renderAvatar = () => {
    const initials = `${studentData.firstName?.charAt(0) || 'U'}${studentData.lastName?.charAt(0) || 'S'}`;
    
    return (
      <View style={styles.avatarContainer}>
        <View style={[styles.avatar, styles.defaultAvatar]}>
          {profileData.photo ? (
            <Image
              source={{ uri: `${imgBaseUrl}/${profileData.photo}` }}
              style={styles.avatarImage}
              resizeMode="cover"
              onError={() => console.log('Error loading profile image')}
            />
          ) : (
            <Text style={styles.avatarText}>{initials.toUpperCase()}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderPersonalInfo = () => (
    <View style={[styles.section, { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }]}>
      <Text style={[styles.sectionTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
        Personal Information
      </Text>

      <View style={styles.infoGrid}>
        <EditableField
          label="First Name"
          value={isEditMode ? editedStudentData.firstName : studentData.firstName}
          onChangeText={(text) => setEditedStudentData(prev => ({ ...prev, firstName: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          error={errors.firstName}
        />

        <EditableField
          label="Middle Name"
          value={isEditMode ? editedStudentData.middleName : studentData.middleName}
          onChangeText={(text) => setEditedStudentData(prev => ({ ...prev, middleName: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
        />

        <EditableField
          label="Last Name"
          value={isEditMode ? editedStudentData.lastName : studentData.lastName}
          onChangeText={(text) => setEditedStudentData(prev => ({ ...prev, lastName: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          error={errors.lastName}
        />

        <EditableField
          label="Mother's Name"
          value={isEditMode ? editedStudentData.mothersName : studentData.mothersName}
          onChangeText={(text) => setEditedStudentData(prev => ({ ...prev, mothersName: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
        />

        <EditableField
          label="Email"
          value={isEditMode ? editedStudentData.email : studentData.email}
          onChangeText={(text) => setEditedStudentData(prev => ({ ...prev, email: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          keyboardType="email-address"
          error={errors.email}
        />

        <EditableField
          label="Contact No"
          value={isEditMode ? editedStudentData.contact : studentData.contact}
          onChangeText={(text) => setEditedStudentData(prev => ({ ...prev, contact: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          keyboardType="phone-pad"
          error={errors.contact}
        />

        <EditableField
          label="Contact Number 2"
          value={isEditMode ? editedProfileData.contactNo2 : profileData.contactNo2}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, contactNo2: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          keyboardType="phone-pad"
          placeholder="Enter alternate contact number"
        />

        <EditableDropdownField
          label="Gender"
          value={isEditMode ? editedProfileData.gender : profileData.gender}
          onChange={(value) => setEditedProfileData(prev => ({ ...prev, gender: value }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          options={genderOptions}
          placeholder="Select Gender"
        />

        <EditableField
          label="Age"
          value={isEditMode ? editedProfileData.age?.toString() : (profileData.age ? `${profileData.age}` : undefined)}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, age: parseInt(text) || undefined }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          keyboardType="numeric"
          placeholder="Enter age"
        />

        <EditableDateField
          label="Date of Birth"
          value={isEditMode ? editedProfileData.birthday : profileData.birthday}
          onChange={(date) => setEditedProfileData(prev => ({ ...prev, birthday: date.toISOString() }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
        />

        <EditableField
          label="Address"
          value={isEditMode ? editedProfileData.address : profileData.address}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, address: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          multiline={true}
          error={errors.address}
        />

        <EditableField
          label="School Name"
          value={isEditMode ? editedProfileData.school : profileData.school}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, school: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          error={errors.school}
        />

        <EditableDropdownField
          label="Medium"
          value={isEditMode ? editedProfileData.medium : profileData.medium}
          onChange={(value) => setEditedProfileData(prev => ({ ...prev, medium: value }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          options={mediumOptions}
          placeholder="Select Medium"
        />

        <EditableDropdownField
          label="Standard"
          value={isEditMode ? editedProfileData.classroom : profileData.classroom}
          onChange={(value) => setEditedProfileData(prev => ({ ...prev, classroom: value }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          options={classroomOptions}
          placeholder="Select Standard"
        />

        {/* Student Image */}
        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Student Image
          </Text>
          {isEditMode ? (
            <View style={styles.imageUploadContainer}>
              <TouchableOpacity
                style={[styles.imageUploadButton, { borderColor: isDarkMode ? '#404040' : '#E8E8E8' }]}
                onPress={showImagePickerOptions}
              >
                {selectedImage || profileData.photo ? (
                  <Image
                    source={{ uri: selectedImage || `${imgBaseUrl}/${profileData.photo}` }}
                    style={styles.uploadedImage}
                    resizeMode="cover"
                  />
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <Ionicons name="camera-outline" size={40} color={isDarkMode ? '#666666' : '#AAAAAA'} />
                    <Text style={[styles.uploadText, { color: isDarkMode ? '#666666' : '#AAAAAA' }]}>
                      Tap to add photo
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
              {(selectedImage || profileData.photo) && (
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={removeImage}
                >
                  <Ionicons name="trash-outline" size={20} color="#F44336" />
                  <Text style={styles.removeImageText}>Remove Image</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <View style={styles.imageDisplayContainer}>
              {profileData.photo ? (
                <Image
                  source={{ uri: `${imgBaseUrl}/${profileData.photo}` }}
                  style={styles.displayImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.noImagePlaceholder}>
                  <Ionicons name="person-outline" size={40} color={isDarkMode ? '#666666' : '#AAAAAA'} />
                  <Text style={[styles.noImageText, { color: isDarkMode ? '#666666' : '#AAAAAA' }]}>
                    No image uploaded
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Identity Document */}
        <View style={styles.infoItem}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
            Identity Document
          </Text>
          {isEditMode ? (
            <View style={styles.documentUploadContainer}>
              <CommonFileUpload
                label=""
                fileName={selectedDocument?.name || (profileData.documentUrl ? 'Current Document' : null)}
                onFileSelect={handleDocumentSelect}
                placeholder="Upload Identity Document"
                style={{ width: '100%', marginBottom: 0 }}
              />
              {(selectedDocument || profileData.documentUrl) && (
                <TouchableOpacity
                  style={styles.removeDocumentButton}
                  onPress={removeDocument}
                >
                  <Ionicons name="trash-outline" size={20} color="#F44336" />
                  <Text style={styles.removeDocumentText}>Remove Document</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <View style={styles.documentDisplayContainer}>
              {profileData.documentUrl ? (
                <TouchableOpacity
                  style={styles.documentViewButton}
                  onPress={() => Linking.openURL(`${imgBaseUrl}/${profileData.documentUrl}`)}
                >
                  <Ionicons name="document-text-outline" size={24} color={PrimaryColors.ORANGE} />
                  <Text style={[styles.documentViewText, { color: PrimaryColors.ORANGE }]}>
                    View Document
                  </Text>
                </TouchableOpacity>
              ) : (
                <Text style={[styles.noDocumentText, { color: isDarkMode ? '#666666' : '#AAAAAA' }]}>
                  No document uploaded
                </Text>
              )}
            </View>
          )}
        </View>
      </View>
    </View>
  );

  const renderOtherInfo = () => (
    <View style={[styles.section, { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }]}>
      <Text style={[styles.sectionTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
        Other Information
      </Text>
      <Text style={[styles.sectionSubtitle, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
        Provide additional details like Aadhaar number, blood group, birth place, etc.
      </Text>

      <View style={styles.infoGrid}>
        <EditableField
          label="Aadhaar Number"
          value={isEditMode ? editedProfileData.aadhaarNo : profileData.aadhaarNo}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, aadhaarNo: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          keyboardType="numeric"
          placeholder="Enter Aadhaar No"
          error={errors.aadhaarNo}
        />

        <EditableDropdownField
          label="Blood Group"
          value={isEditMode ? editedProfileData.bloodGroup : profileData.bloodGroup}
          onChange={(value) => setEditedProfileData(prev => ({ ...prev, bloodGroup: value }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          options={bloodGroupOptions}
          placeholder="Select Blood Group"
        />

        <EditableField
          label="Birth Place"
          value={isEditMode ? editedProfileData.birthPlace : profileData.birthPlace}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, birthPlace: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          placeholder="Enter Birth Place"
        />

        <EditableField
          label="Mother Tongue"
          value={isEditMode ? editedProfileData.motherTongue : profileData.motherTongue}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, motherTongue: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          placeholder="Enter Mother Tongue"
        />

        <EditableField
          label="Religion"
          value={isEditMode ? editedProfileData.religion : profileData.religion}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, religion: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          placeholder="Enter Religion"
        />

        <EditableField
          label="Caste"
          value={isEditMode ? editedProfileData.caste : profileData.caste}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, caste: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          placeholder="Enter Caste"
        />

        <EditableField
          label="Sub Caste"
          value={isEditMode ? editedProfileData.subCaste : profileData.subCaste}
          onChangeText={(text) => setEditedProfileData(prev => ({ ...prev, subCaste: text }))}
          isEditMode={isEditMode}
          isDarkMode={isDarkMode}
          placeholder="Enter Sub Caste"
        />
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaProvider>
        <NavigationHeader title="Profile" onBackPress={() => navigation.goBack()} />
        <SafeAreaView style={[styles.container, { backgroundColor: isDarkMode ? PrimaryColors.LIGHTGRAY : '#F8FAFC' }]}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={PrimaryColors.ORANGE} />
            <Text style={[styles.loadingText, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
              Loading profile...
            </Text>
          </View>
        </SafeAreaView>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationHeader title="Profile" onBackPress={() => navigation.goBack()} />
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? PrimaryColors.LIGHTGRAY : '#F8FAFC' }
        ]}
        edges={['left', 'right']}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          {/* Profile Header */}
          <View style={[styles.profileHeader, { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }]}>
            {renderAvatar()}
            <Text style={[styles.profileName, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
              {studentData.firstName} {studentData.lastName}
            </Text>
            <Text style={[styles.profileEmail, { color: isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW }]}>
              {studentData.email}
            </Text>
            
            <View style={styles.buttonContainer}>
              {isEditMode ? (
                <>
                  <TouchableOpacity
                    style={[styles.editButton, styles.saveButton]}
                    onPress={handleSave}
                    disabled={saving}
                  >
                    {saving ? (
                      <ActivityIndicator size="small" color={PrimaryColors.WHITE} />
                    ) : (
                      <>
                        <Ionicons name="checkmark-outline" size={20} color={PrimaryColors.WHITE} />
                        <Text style={styles.editButtonText}>Save</Text>
                      </>
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.editButton, styles.cancelButton]}
                    onPress={handleCancel}
                    disabled={saving}
                  >
                    <Ionicons name="close-outline" size={20} color={PrimaryColors.WHITE} />
                    <Text style={styles.editButtonText}>Cancel</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => setIsEditMode(true)}
                >
                  <Ionicons name="create-outline" size={20} color={PrimaryColors.WHITE} />
                  <Text style={styles.editButtonText}>Edit Profile</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'Personal' && styles.activeTab,
                { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }
              ]}
              onPress={() => setActiveTab('Personal')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'Personal' && styles.activeTabText,
                { color: activeTab === 'Personal' ? PrimaryColors.ORANGE : (isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW) }
              ]}>
                Personal Info
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'Other' && styles.activeTab,
                { backgroundColor: isDarkMode ? PrimaryColors.CARDBACKGROUNDDARK : PrimaryColors.WHITE }
              ]}
              onPress={() => setActiveTab('Other')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'Other' && styles.activeTabText,
                { color: activeTab === 'Other' ? PrimaryColors.ORANGE : (isDarkMode ? '#8D8D8D' : PrimaryColors.GRAYSHADOW) }
              ]}>
                Other Info
              </Text>
            </TouchableOpacity>
          </View>

          {/* Content */}
          {activeTab === 'Personal' ? renderPersonalInfo() : renderOtherInfo()}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  profileHeader: {
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  defaultAvatar: {
    backgroundColor: PrimaryColors.ORANGE,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  avatarText: {
    color: PrimaryColors.WHITE,
    fontSize: 28,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  profileEmail: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: PrimaryColors.ORANGE,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
  },
  saveButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  editButtonText: {
    color: PrimaryColors.WHITE,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: PrimaryColors.ORANGE,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  activeTabText: {
    color: PrimaryColors.ORANGE,
  },
  section: {
    marginBottom: 16,
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 20,
    lineHeight: 20,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '400',
    paddingBottom: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  infoInput: {
    fontSize: 16,
    fontWeight: '400',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 4,
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  // Image upload styles
  imageUploadContainer: {
    marginBottom: 4,
  },
  imageUploadButton: {
    width: 120,
    height: 120,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  uploadedImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  uploadPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  removeImageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  removeImageText: {
    color: '#F44336',
    fontSize: 14,
    marginLeft: 8,
  },
  imageDisplayContainer: {
    marginBottom: 4,
  },
  displayImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
  },
  noImagePlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noImageText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  // Document upload styles
  documentUploadContainer: {
    marginBottom: 4,
  },
  removeDocumentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    marginTop: 8,
  },
  removeDocumentText: {
    color: '#F44336',
    fontSize: 14,
    marginLeft: 8,
  },
  documentDisplayContainer: {
    marginBottom: 4,
  },
  documentViewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 152, 0, 0.3)',
  },
  documentViewText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  noDocumentText: {
    fontSize: 16,
    paddingVertical: 12,
  },
});

export default StudentProfileInfo;
