import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import { getThemeColors, IMAGE_CONSTANT } from '../../../Utils/Constants';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getMyOrders, StoreOrder } from '../../../services/storePurchaseService';
import { imgBaseUrl } from '../../../config/apiUrl';

const OrderHistory: React.FC = () => {
  const navigation = useNavigation<any>();
  const { isDarkMode } = IndexStyle();
  const themeColors = getThemeColors(isDarkMode);
  const [orders, setOrders] = useState<StoreOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const ordersResult = await getMyOrders();
      if (ordersResult.success && ordersResult.data) {
        setOrders(ordersResult.data);
      } else {
        setError(ordersResult.error || 'Failed to fetch orders');
      }
    } catch (err) {
      setError('Failed to load order history');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setIsRefreshing(true);
    await fetchOrders();
    setIsRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return '#4CAF50';
      case 'PENDING':
        return '#FF9800';
      case 'CANCELLED':
        return '#F44336';
      default:
        return themeColors.muted;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Completed';
      case 'PENDING':
        return 'Pending';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderOrderItem = ({ item }: { item: StoreOrder }) => (
    <TouchableOpacity 
      style={[styles.orderCard, { backgroundColor: themeColors.card }]}
      onPress={() => {
        // Navigate to order details if needed
        Alert.alert(
          'Order Details',
          `Order ID: ${item.id}\nItem: ${item.itemName}\nQuantity: ${item.quantity}\nTotal: ${item.totalCoins} UEST Coins\nStatus: ${getStatusText(item.status)}\nDate: ${formatDate(item.createdAt)}`,
          [{ text: 'OK' }]
        );
      }}
    >
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <Text style={[styles.orderId, { color: themeColors.text }]}>
            Order #{item.id.slice(-8).toUpperCase()}
          </Text>
          <Text style={[styles.orderDate, { color: themeColors.muted }]}>
            {formatDate(item.createdAt)}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>

      <View style={styles.orderContent}>
        <View style={styles.itemInfo}>
          {item.item?.image ? (
            <Image
              source={{ uri: `${imgBaseUrl}${item.item.image}` }}
              style={styles.itemImage}
              resizeMode="contain"
              defaultSource={IMAGE_CONSTANT.STORE}
              onError={() => {
                console.log('Failed to load order item image:', item.item.image);
              }}
            />
          ) : (
            <Image
              source={IMAGE_CONSTANT.STORE}
              style={styles.itemImage}
              resizeMode="contain"
            />
          )}
          <View style={styles.itemDetails}>
            <Text style={[styles.itemName, { color: themeColors.text }]} numberOfLines={2}>
              {item.itemName}
            </Text>
            <Text style={[styles.itemCategory, { color: themeColors.muted }]}>
              {item.item?.category?.charAt(0).toUpperCase() + item.item?.category?.slice(1) || 'Unknown'}
            </Text>
            <Text style={[styles.itemQuantity, { color: themeColors.muted }]}>
              Quantity: {item.quantity}
            </Text>
          </View>
        </View>
        
        <View style={styles.priceInfo}>
          <View style={styles.priceRow}>
            <Image
              source={IMAGE_CONSTANT.NEWUESTCOIN}
              style={styles.coinIcon}
              resizeMode="contain"
            />
            <Text style={[styles.totalPrice, { color: themeColors.text }]}>
              {item.totalCoins}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="receipt-outline" size={64} color={themeColors.muted} />
      <Text style={[styles.emptyTitle, { color: themeColors.text }]}>
        No Orders Yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: themeColors.muted }]}>
        Your order history will appear here once you make your first purchase.
      </Text>
      <TouchableOpacity
        style={[styles.shopButton, { backgroundColor: themeColors.accent }]}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.shopButtonText}>Start Shopping</Text>
      </TouchableOpacity>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Ionicons name="alert-circle-outline" size={64} color="#F44336" />
      <Text style={[styles.errorTitle, { color: '#F44336' }]}>
        Failed to Load Orders
      </Text>
      <Text style={[styles.errorSubtitle, { color: themeColors.muted }]}>
        {error}
      </Text>
      <TouchableOpacity
        style={[styles.retryButton, { backgroundColor: themeColors.accent }]}
        onPress={fetchOrders}
      >
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[styles.container, { backgroundColor: themeColors.background }]}
        edges={['left', 'right']}
      >
        <NavigationHeader
          title="Order History"
          isBack={true}
          onBackPress={() => navigation.goBack()}
        />

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={themeColors.accent} />
            <Text style={[styles.loadingText, { color: themeColors.muted }]}>
              Loading your orders...
            </Text>
          </View>
        ) : error ? (
          renderErrorState()
        ) : orders.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={orders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.ordersList}
            showsVerticalScrollIndicator={false}
            scrollEnabled={true}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={[themeColors.accent]}
                tintColor={themeColors.accent}
              />
            }
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        )}
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  ordersList: {
    padding: 16,
  },
  orderCard: {
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  orderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: '#F8F8F8',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemCategory: {
    fontSize: 12,
    marginBottom: 2,
    textTransform: 'capitalize',
  },
  itemQuantity: {
    fontSize: 12,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinIcon: {
    width: 20,
    height: 20,
    marginRight: 4,
  },
  totalPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  separator: {
    height: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  shopButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  shopButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OrderHistory;
